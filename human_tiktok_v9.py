import random
import time
import subprocess
import threading
import traceback
from typing import List, Dict, Any
from datetime import datetime

from comment import get_response  # your comment.py
from appium import webdriver
from appium.options.android import UiAutomator2Options
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException
from kill_appium_ports import kill_appium_ports

# -------------------------------------------------------------------
# CONFIGURATION
# -------------------------------------------------------------------
TEST_MODE = True
TEST_DURATION = 1000

APPIUM_CMD = "appium"
APP_HOST = "127.0.0.1"
# Update APP_PORTS based on DEVICES list
DEVICES = [
    {"udid": "R5CN907GGRA", "systemPort": 8200, "appPort": 4723},
    {"udid": "R5CT52WVQLW", "systemPort": 8201, "appPort": 4725},
]
APP_PORTS = [device["appPort"] for device in DEVICES]
STARTUP_DELAY = 2

def get_connected_devices() -> List[str]:
    """Get list of connected Android device UDIDs using adb."""
    try:
        result = subprocess.run(
            ['adb', 'devices'],
            capture_output=True,
            text=True,
            check=True
        )
        # Split output by lines, skip first line (header) and filter out empty lines and unauthorized devices
        devices = [
            line.split('\t')[0] 
            for line in result.stdout.split('\n')[1:] 
            if line.strip() and '\toffline' not in line and '\tdevice' in line
        ]
        return devices
    except subprocess.CalledProcessError as e:
        print(f"Error getting connected devices: {e}")
        return []

def update_devices() -> List[Dict[str, Any]]:
    """Update DEVICES list based on connected ADB devices."""
    device_udids = get_connected_devices()
    updated_devices = []
    
    for i, udid in enumerate(device_udids, start=0):
        device = {
            "udid": udid,
            "systemPort": 8200 + i,
            "appPort": 4723 + (i * 2)  # Increment by 2 for app ports
        }
        updated_devices.append(device)
    
    return updated_devices

# Update DEVICES list if UPDATE_DEVICES is True, otherwise use the static list
UPDATE_DEVICES = True
if UPDATE_DEVICES:
    DEVICES = update_devices()

kill_appium_ports([device["systemPort"] for device in DEVICES])

APP_PKG = "com.zhiliaoapp.musically"
APP_ACTIVITY = "com.ss.android.ugc.aweme.splash.SplashActivity"
WAIT_TIMEOUT = 15

BASE_CAPS = {
    "platformName": "Android",
    "automationName": "UiAutomator2",
    "deviceName": "Android Device",
    "appPackage": APP_PKG,
    "appActivity": APP_ACTIVITY,
    "noReset": True,
    "newCommandTimeout": 3600,
    # speed-ups
    "settings[waitForIdleTimeout]": 0,
    "settings[waitForSelectorTimeout]": 0,
    "disableWindowAnimation": True,
}
# -------------------------------------------------------------------


def start_appium(port: int, logfile: str) -> subprocess.Popen:
    cmd = [APPIUM_CMD, "--port", str(port), "--log-level", "info"]
    f = open(logfile, "w")
    print(f"→ Starting Appium on {port} → {logfile}")
    return subprocess.Popen(cmd, stdout=f, stderr=subprocess.STDOUT)


def generate_session_times(n: int, start_hour=9, end_hour=22):
    from datetime import timedelta

    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    st = today + timedelta(hours=start_hour)
    en = today + timedelta(hours=end_hour)
    span = (en - st).total_seconds()
    offs = sorted(random.uniform(0, span) for _ in range(n))
    return [st + timedelta(seconds=o) for o in offs]


ACTIONS = [
    ("scroll", 0.59),
    ("like", 0.20),
    ("save", 0.05),
    ("comment", 0.02),
    ("read_comments", 0.10),
    ("rewatch", 0.05),
    ("follow", 0.01),
    ("share_repost", 0.01),
]


class TikTokBot:
    def __init__(self, driver, wait, rng, udid):
        self.driver = driver
        self.wait = wait
        self.rng = rng
        self.udid = udid
        self.stats = {name: 0 for name, _ in ACTIONS}
        self.actions = {
            "scroll": self.do_scroll,
            "like": self.do_like,
            "save": self.do_save,
            "comment": self.do_comment,
            "read_comments": self.do_read_comments,
            "rewatch": self.do_rewatch,
            "follow": self.do_follow,
            "share_repost": self.do_share_repost,
        }

    def log(self, msg):
        print(f"[{self.udid}] {msg}")

    def random_dwell(self):
        r = self.rng.random()
        if r < 0.7:
            return self.rng.uniform(1, 2)
        if r < 0.9:
            return self.rng.uniform(3, 6)
        return self.rng.uniform(10, 20)

    def choose_action(self):
        r, cum = self.rng.random(), 0
        for name, p in ACTIONS:
            cum += p
            if r < cum:
                return name
        return "scroll"

    def restart_app(self):
        self.log("Action: restart TikTok")
        try:
            self.driver.terminate_app(APP_PKG)
        except:
            pass
        time.sleep(1 + self.rng.random())
        self.driver.activate_app(APP_PKG)
        self.wait.until(
            EC.presence_of_element_located((AppiumBy.ID, f"{APP_PKG}:id/w3v"))
        )
        self.wait.until(
            EC.presence_of_element_located((AppiumBy.ID, f"{APP_PKG}:id/ebb"))
        )
        self.log("→ Relaunched")

    def do_scroll(self):
        self.log("Action: scroll")
        w, h = self.driver.get_window_size().values()
        sx = self.rng.randint(int(w * 0.2), int(w * 0.8))
        sy = self.rng.randint(int(h * 0.7), int(h * 0.9))
        ex = self.rng.randint(int(w * 0.2), int(w * 0.8))
        ey = self.rng.randint(int(h * 0.1), int(h * 0.3))
        dur = self.rng.randint(150, 300)
        self.driver.swipe(sx, sy, ex, ey, dur)

    def do_like(self):
        self.log("Action: like")
        elt = self.driver.find_element(AppiumBy.ID, f"{APP_PKG}:id/ebb")
        self.driver.execute_script("mobile: clickGesture", {"elementId": elt.id})

    def do_save(self):
        self.log("Action: save")
        btn = self.driver.find_element(AppiumBy.ID, f"{APP_PKG}:id/frn")
        self.driver.execute_script("mobile: clickGesture", {"elementId": btn.id})

    def do_comment(self):
        self.log("Action: comment")
        # 1) take screenshot
        self.driver.save_screenshot("screenshot.png")
        self.log("↳ screenshot.png saved")
        # 2) delegate to comment.py
        comment = get_response()  # from human_tiktok_comment.py
        if comment:
            self.log(f"  get_response→ {comment}")
        else:
            comment = self.rng.choice(["Nice!", "😂", "Love it!"])
            self.log(f"  fallback→ {comment}")
        # 3) open comment UI
        opener = self.driver.find_element(AppiumBy.ID, f"{APP_PKG}:id/d8g")
        self.driver.execute_script("mobile: clickGesture", {"elementId": opener.id})
        self.wait.until(
            EC.presence_of_element_located(
                (AppiumBy.CLASS_NAME, "android.widget.EditText")
            )
        )
        inp = self.driver.find_element(AppiumBy.CLASS_NAME, "android.widget.EditText")
        inp.clear()
        inp.send_keys(comment)
        time.sleep(0.5)
        try:
            send = self.driver.find_element(AppiumBy.ID, f"{APP_PKG}:id/d76")
            self.driver.execute_script("mobile: clickGesture", {"elementId": send.id})
            self.log("  → Sent")
        except NoSuchElementException:
            self.log("  ⚠ send id/d76 missing")
        time.sleep(1)
        self.driver.back()

    def do_read_comments(self):
        self.log("Action: read_comments")
        opener = self.driver.find_element(AppiumBy.ID, f"{APP_PKG}:id/d8g")
        self.driver.execute_script("mobile: clickGesture", {"elementId": opener.id})
        for _ in range(self.rng.randint(1, 4)):
            self.do_scroll()
            time.sleep(self.rng.uniform(0.5, 1.5))
        self.driver.back()

    def do_rewatch(self):
        self.log("Action: rewatch")
        w, h = self.driver.get_window_size().values()
        self.driver.swipe(w // 2, int(h * 0.2), w // 2, int(h * 0.8), 200)
        time.sleep(self.rng.uniform(0.5, 1.0))
        self.driver.swipe(w // 2, int(h * 0.8), w // 2, int(h * 0.2), 200)

    def do_follow(self):
        self.log("Action: follow")
        try:
            b = self.driver.find_element(AppiumBy.ID, f"{APP_PKG}:id/gd_")
            self.driver.execute_script("mobile: clickGesture", {"elementId": b.id})
        except NoSuchElementException:
            self.log("  ⚠ follow not found")

    def do_share_repost(self):
        self.log("Action: share_repost")
        try:
            s = self.driver.find_element(
                AppiumBy.ANDROID_UIAUTOMATOR,
                'new UiSelector().descriptionContains("Share")',
            )
            self.driver.execute_script("mobile: clickGesture", {"elementId": s.id})
        except NoSuchElementException:
            self.log("  ⚠ share not found")
            return
        self.wait.until(
            EC.presence_of_element_located((AppiumBy.ID, f"{APP_PKG}:id/v71"))
        )
        try:
            r = self.driver.find_element(AppiumBy.ID, f"{APP_PKG}:id/v71")
            self.driver.execute_script("mobile: clickGesture", {"elementId": r.id})
            self.log("  → Reposted")
        except NoSuchElementException:
            self.log("  ⚠ repost id/v71 missing")

    def choose_and_run(self):
        nm = self.choose_action()
        self.stats[nm] += 1
        self.log(f"> {nm} (#{self.stats[nm]})")
        self.actions[nm]()
        dwell = self.random_dwell()
        self.log(f"  dwell {dwell:.1f}s")
        time.sleep(dwell)

    def print_stats(self):
        self.log("Stats:")
        print(f"{'Action':<15}{'Count':<5}")
        for name, _ in ACTIONS:
            print(f"{name:<15}{self.stats[name]:<5}")
        print("-" * 20)

    def run_session(self, dur: float):
        end = time.time() + dur
        while time.time() < end:
            self.choose_and_run()


def run_bot(udid, sp, ap):
    seed = int(time.time()) ^ hash(udid)
    rng = random.Random(seed)
    print(f"[{udid}] seed={seed}")

    caps = BASE_CAPS.copy()
    caps["udid"] = udid
    caps["systemPort"] = sp
    url = f"http://{APP_HOST}:{ap}"
    opts = UiAutomator2Options().load_capabilities(caps)
    drv = webdriver.Remote(url, options=opts)
    wait = WebDriverWait(drv, WAIT_TIMEOUT)
    bot = TikTokBot(drv, wait, rng, udid)

    if TEST_MODE:
        bot.restart_app()
        bot.run_session(TEST_DURATION)
        bot.print_stats()
        drv.quit()
        return

    sess = generate_session_times(rng.randint(3, 5))
    print(f"[{udid}] sessions: {sess}")

    for ts in sess:
        delta = (ts - datetime.now()).total_seconds()
        if delta > 0:
            time.sleep(delta)
        bot.stats = {n: 0 for n, _ in ACTIONS}
        bot.restart_app()
        length = max(60, rng.gauss(15 * 60, 5 * 60))
        print(f"[{udid}] run {length / 60:.1f}m")
        bot.run_session(length)
        bot.print_stats()
        try:
            drv.terminate_app(APP_PKG)
        except:
            pass
        time.sleep(rng.uniform(2, 5))

    drv.quit()
    print(f"[{udid}] done")


def run_bot_with_retry(udid, sp, ap, thread_manager):
    """Wrapper function that runs the bot with error handling and retry logic."""
    max_retries = 99
    retry_count = 0

    while retry_count < max_retries and not thread_manager.should_stop:
        try:
            print(f"[{udid}] Starting bot (attempt {retry_count + 1}/{max_retries})")
            run_bot(udid, sp, ap)
            print(f"[{udid}] Bot completed successfully")
            break

        except KeyboardInterrupt:
            print(f"[{udid}] Received keyboard interrupt, stopping...")
            break

        except Exception as e:
            retry_count += 1
            error_msg = f"[{udid}] ERROR in thread (attempt {retry_count}/{max_retries}): {str(e)}"
            print(error_msg)
            print(f"[{udid}] Full traceback:")
            traceback.print_exc()

            if retry_count < max_retries and not thread_manager.should_stop:
                # Kill appium ports for this specific device before retry
                print(f"[{udid}] Killing appium ports for device before retry...")
                try:
                    kill_appium_ports([ap, sp])  # Kill both app port and system port
                    print(f"[{udid}] Successfully killed ports {ap} and {sp}")
                except Exception as port_error:
                    print(f"[{udid}] Warning: Failed to kill ports: {port_error}")

                # Much shorter timeout for retry
                wait_time = min(5 + retry_count, 30)  # Start at 6 seconds, max 30 seconds
                print(f"[{udid}] Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                print(f"[{udid}] Max retries reached or stop requested, thread ending")
                break

    # Mark this thread as completed in the thread manager
    thread_manager.mark_thread_completed(udid)


class ThreadManager:
    """Manages threads and handles automatic restart of failed threads."""

    def __init__(self, devices):
        self.devices = devices
        self.active_threads = {}
        self.should_stop = False
        self.lock = threading.Lock()

    def start_thread(self, device):
        """Start a new thread for the given device."""
        udid = device["udid"]
        sp = device["systemPort"]
        ap = device["appPort"]

        with self.lock:
            # Stop existing thread if it exists
            if udid in self.active_threads:
                old_thread = self.active_threads[udid]
                if old_thread.is_alive():
                    print(f"[{udid}] Warning: Previous thread still alive, continuing with new thread")

            # Create and start new thread
            thread = threading.Thread(
                target=run_bot_with_retry,
                args=(udid, sp, ap, self),
                daemon=True,
                name=f"Bot-{udid}"
            )
            self.active_threads[udid] = thread
            thread.start()
            print(f"[{udid}] Thread started")

    def mark_thread_completed(self, udid):
        """Mark a thread as completed."""
        with self.lock:
            if udid in self.active_threads:
                print(f"[{udid}] Thread marked as completed")

    def monitor_and_restart_threads(self):
        """Monitor threads and restart any that have died unexpectedly."""
        while not self.should_stop:
            try:
                time.sleep(10)  # Check every 10 seconds

                with self.lock:
                    for device in self.devices:
                        udid = device["udid"]

                        if udid not in self.active_threads:
                            print(f"[{udid}] No thread found, starting new thread")
                            self.start_thread(device)
                        else:
                            thread = self.active_threads[udid]
                            if not thread.is_alive():
                                print(f"[{udid}] Thread died, restarting...")
                                self.start_thread(device)

            except Exception as e:
                print(f"[ThreadManager] Error in monitor loop: {e}")
                traceback.print_exc()
                time.sleep(5)

    def start_all_threads(self):
        """Start threads for all devices."""
        for device in self.devices:
            self.start_thread(device)

    def stop_all_threads(self):
        """Signal all threads to stop."""
        self.should_stop = True
        print("[ThreadManager] Stopping all threads...")

    def wait_for_all_threads(self):
        """Wait for all threads to complete."""
        while True:
            with self.lock:
                alive_threads = [t for t in self.active_threads.values() if t.is_alive()]
                if not alive_threads:
                    break

            print(f"[ThreadManager] Waiting for {len(alive_threads)} threads to complete...")
            time.sleep(1)


if __name__ == "__main__":
    print("Starting TikTok Bot Farm with enhanced error handling...")
    print(f"Devices configured: {len(DEVICES)}")
    for device in DEVICES:
        print(f"  - {device['udid']} (port: {device['appPort']}, systemPort: {device['systemPort']})")

    # Start Appium servers
    servers = [start_appium(p, f"appium-{p}.log") for p in APP_PORTS]
    time.sleep(STARTUP_DELAY)

    # Create thread manager
    thread_manager = ThreadManager(DEVICES)

    try:
        # Start all bot threads
        thread_manager.start_all_threads()

        # Start monitoring thread
        monitor_thread = threading.Thread(
            target=thread_manager.monitor_and_restart_threads,
            daemon=True,
            name="ThreadMonitor"
        )
        monitor_thread.start()

        print("All threads started. Press Ctrl+C to stop...")

        # Keep main thread alive and wait for keyboard interrupt
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("🛑 Keyboard interrupt received, shutting down...")

    finally:
        # Stop all threads
        thread_manager.stop_all_threads()

        # Wait a bit for threads to stop gracefully
        print("Waiting for threads to stop...")
        time.sleep(5)

        # Terminate Appium servers
        print("Terminating Appium servers...")
        for s in servers:
            try:
                s.terminate()
                s.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"Force killing Appium server {s.pid}")
                s.kill()
            except Exception as e:
                print(f"Error terminating server: {e}")

        print("All Appium servers terminated.")
        print("Shutdown complete.")
