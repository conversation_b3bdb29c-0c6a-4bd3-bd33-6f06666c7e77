import time
import subprocess
from threading import Thread

from appium import webdriver
from appium.options.android import UiAutomator2Options
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

APPIUM_CMD = "appium"  # must be in your PATH
APP_HOST = "127.0.0.1"
SERVERS = [4723, 4725]  # ports for two Appium servers
STARTUP_DELAY = 5  # secs to wait for servers to become ready

# base caps; we'll override `udid`, `systemPort` per session
BASE_CAPS = {
    "platformName": "Android",
    "automationName": "UiAutomator2",
    "deviceName": "Android Device",
    "appPackage": "com.zhiliaoapp.musically",
    "appActivity": "com.ss.android.ugc.aweme.splash.SplashActivity",
    "noReset": True,
    "newCommandTimeout": 3600,
}


def start_appium(port: int, logfile: str) -> subprocess.Popen:
    """
    Launches an Appium server on the given port, redirecting output
    to `logfile`. Returns the Popen handle.
    """
    f = open(logfile, "w")
    cmd = [APPIUM_CMD, "--port", str(port), "--log-level", "info"]
    print(f"→ Starting Appium on port {port} -> {logfile}")
    return subprocess.Popen(cmd, stdout=f, stderr=subprocess.STDOUT)


def run_session(udid: str, system_port: int, server_port: int):
    """
    Connects to Appium at server_port, drives the TikTok feed on the
    device with udid, liking every 3rd video.
    """
    caps = BASE_CAPS.copy()
    caps["udid"] = udid
    caps["systemPort"] = system_port

    url = f"http://{APP_HOST}:{server_port}"
    opts = UiAutomator2Options().load_capabilities(caps)
    print(f"[{udid}] → Connecting to {url} using systemPort={system_port}")
    driver = webdriver.Remote(url, options=opts)
    wait = WebDriverWait(driver, 15)
    count = 0

    try:
        print(f"[{udid}] Current activity: {driver.current_activity}")
        while True:
            # wait for the feed container
            wait.until(
                EC.presence_of_element_located(
                    (AppiumBy.ID, "com.zhiliaoapp.musically:id/w3v")
                )
            )
            time.sleep(2)  # allow UI to settle

            count += 1
            print(f"[{udid}] Scrolled to video #{count}")

            if count % 3 == 0:
                print(f"[{udid}] Liking video…")
                try:
                    btn = driver.find_element(
                        AppiumBy.ID, "com.zhiliaoapp.musically:id/ebb"
                    )
                    btn.click()
                    print(f"[{udid}] Liked!")
                except NoSuchElementException:
                    print(f"[{udid}] Like button not found")

            # swipe up
            w, h = driver.get_window_size().values()
            driver.swipe(w // 2, int(h * 0.8), w // 2, int(h * 0.2), 400)

    except KeyboardInterrupt:
        print(f"\n[{udid}] Interrupted by user")
    except TimeoutException:
        print(f"[{udid}] Feed container not found – exiting")
    finally:
        driver.quit()
        print(f"[{udid}] Session closed")


if __name__ == "__main__":
    # your two device udids + unique systemPorts
    devices = [
        {"udid": "************:7409", "systemPort": 8200, "serverPort": 4723},
        {"udid": "************:7417", "systemPort": 8201, "serverPort": 4725},
    ]

    # 1) start both Appium servers
    servers = []
    for p in SERVERS:
        servers.append(start_appium(p, f"appium-{p}.log"))

    # 2) give them time to spin up
    time.sleep(STARTUP_DELAY)

    # 3) spawn one thread per device/session
    threads = []
    for d in devices:
        t = Thread(
            target=run_session,
            args=(d["udid"], d["systemPort"], d["serverPort"]),
            daemon=True,
        )
        threads.append(t)
        t.start()

    # 4) wait for user interrupt / sessions to complete
    try:
        for t in threads:
            t.join()
    except KeyboardInterrupt:
        print("⛔ User requested shutdown")
    finally:
        # tear down Appium servers
        for proc in servers:
            proc.terminate()
        print("All Appium servers terminated")
